import { r as runBaseTests } from '../vendor/base.Ybri3C14.js';
import { a as createThreadsRpcOptions } from '../vendor/utils.0uYuCbzo.js';
import 'vite-node/client';
import '../vendor/global.CkGT_TMy.js';
import '../vendor/execute.fL3szUAI.js';
import 'node:vm';
import 'node:url';
import 'node:fs';
import 'vite-node/utils';
import 'pathe';
import '@vitest/utils/error';
import '../path.js';
import '@vitest/utils';
import '../vendor/base.5NT-gWu5.js';

class ThreadsBaseWorker {
  getRpcOptions(ctx) {
    return createThreadsRpcOptions(ctx);
  }
  runTests(state) {
    return runBaseTests(state);
  }
}
var threads = new ThreadsBaseWorker();

export { threads as default };
