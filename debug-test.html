<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="test-results"></div>
    
    <script>
        async function testApplication() {
            const results = document.getElementById('test-results');
            
            try {
                // Test if the main page loads
                const response = await fetch('http://localhost:5173');
                const html = await response.text();
                
                results.innerHTML += '<p>✅ Main page loads successfully</p>';
                
                // Check if the app div exists
                if (html.includes('<div id="app">')) {
                    results.innerHTML += '<p>✅ App div found in HTML</p>';
                } else {
                    results.innerHTML += '<p>❌ App div not found in HTML</p>';
                }
                
                // Test if main.js loads
                const jsResponse = await fetch('http://localhost:5173/src/main.js');
                if (jsResponse.ok) {
                    results.innerHTML += '<p>✅ main.js loads successfully</p>';
                } else {
                    results.innerHTML += '<p>❌ main.js failed to load</p>';
                }
                
                // Test if App.svelte loads
                const appResponse = await fetch('http://localhost:5173/src/App.svelte');
                if (appResponse.ok) {
                    results.innerHTML += '<p>✅ App.svelte loads successfully</p>';
                } else {
                    results.innerHTML += '<p>❌ App.svelte failed to load</p>';
                }
                
                // Test API connection
                const apiResponse = await fetch('http://localhost:9000/health');
                if (apiResponse.ok) {
                    const apiData = await apiResponse.json();
                    results.innerHTML += '<p>✅ TPA API is healthy: ' + apiData.status + '</p>';
                } else {
                    results.innerHTML += '<p>❌ TPA API is not responding</p>';
                }
                
            } catch (error) {
                results.innerHTML += '<p>❌ Error: ' + error.message + '</p>';
            }
        }
        
        testApplication();
    </script>
</body>
</html>
