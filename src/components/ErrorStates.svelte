<!--
  ErrorStates.svelte
  
  Reusable error state components with user-friendly messages and recovery options.
  
  Features:
  - Multiple error state variants (network, api, validation, not-found)
  - User-friendly error messages
  - Retry mechanisms and recovery actions
  - Responsive design with Tailwind CSS
  - Accessible error indicators with ARIA labels
  - Customizable error messages and actions
  
  Usage:
  <ErrorStates variant="network" on:retry />
  <ErrorStates variant="not-found" message="Policy not found" />
  <ErrorStates variant="api" error={apiError} on:retry />
-->

<script>
  import { createEventDispatcher } from "svelte";
  
  export let variant = "generic"; // "network", "api", "validation", "not-found", "generic"
  export let message = ""; // Custom error message
  export let error = null; // Error object for detailed information
  export let showRetry = true; // Whether to show retry button
  export let retryText = "Try Again"; // Retry button text
  export let showDetails = false; // Whether to show error details
  
  const dispatch = createEventDispatcher();
  
  // Default messages for different error types
  const defaultMessages = {
    network: "Unable to connect to the server. Please check your internet connection and try again.",
    api: "We're experiencing technical difficulties. Please try again in a moment.",
    validation: "There was an issue with the provided information. Please check your input and try again.",
    "not-found": "The requested information could not be found.",
    generic: "Something went wrong. Please try again."
  };
  
  // Get appropriate error message
  $: errorMessage = message || (error?.getUserMessage?.() || error?.message) || defaultMessages[variant] || defaultMessages.generic;
  
  // Get error icon based on variant
  function getErrorIcon(variant) {
    switch (variant) {
      case "network":
        return "🌐";
      case "api":
        return "⚠️";
      case "validation":
        return "❌";
      case "not-found":
        return "🔍";
      default:
        return "⚠️";
    }
  }
  
  // Handle retry action
  function handleRetry() {
    dispatch("retry");
  }
  
  // Handle details toggle
  function toggleDetails() {
    showDetails = !showDetails;
  }
</script>

<div class="flex flex-col items-center justify-center py-12 px-4 text-center" 
     role="alert" 
     aria-live="polite">
  
  <!-- Error Icon -->
  <div class="text-6xl mb-4" aria-hidden="true">
    {getErrorIcon(variant)}
  </div>
  
  <!-- Error Title -->
  <h2 class="text-2xl font-bold text-gray-900 mb-2">
    {#if variant === "network"}
      Connection Problem
    {:else if variant === "api"}
      Service Unavailable
    {:else if variant === "validation"}
      Invalid Information
    {:else if variant === "not-found"}
      Not Found
    {:else}
      Something Went Wrong
    {/if}
  </h2>
  
  <!-- Error Message -->
  <p class="text-gray-600 max-w-md mb-6 leading-relaxed">
    {errorMessage}
  </p>
  
  <!-- Action Buttons -->
  <div class="flex flex-col sm:flex-row gap-3 items-center">
    {#if showRetry}
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleRetry}
        aria-label="Retry the failed operation"
      >
        <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {retryText}
      </button>
    {/if}
    
    <!-- Go Back Button (for not-found errors) -->
    {#if variant === "not-found"}
      <button
        class="inline-flex items-center px-6 py-3
               bg-gray-600 hover:bg-gray-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        on:click={() => dispatch("goBack")}
        aria-label="Go back to previous page"
      >
        <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M15 19l-7-7 7-7" />
        </svg>
        Go Back
      </button>
    {/if}
    
    <!-- Show Details Button -->
    {#if error && (error.status || error.stack)}
      <button
        class="text-gray-500 hover:text-gray-700 text-sm font-medium
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"
        on:click={toggleDetails}
        aria-label="Toggle error details"
      >
        {showDetails ? "Hide Details" : "Show Details"}
      </button>
    {/if}
  </div>
  
  <!-- Error Details (collapsible) -->
  {#if showDetails && error}
    <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200 max-w-2xl w-full">
      <h3 class="text-sm font-semibold text-gray-900 mb-2">Error Details</h3>
      <div class="text-left space-y-2">
        {#if error.status}
          <div class="text-xs">
            <span class="font-medium text-gray-700">Status:</span>
            <span class="text-gray-600 font-mono">{error.status}</span>
          </div>
        {/if}
        {#if error.code}
          <div class="text-xs">
            <span class="font-medium text-gray-700">Code:</span>
            <span class="text-gray-600 font-mono">{error.code}</span>
          </div>
        {/if}
        {#if error.message && error.message !== errorMessage}
          <div class="text-xs">
            <span class="font-medium text-gray-700">Technical Message:</span>
            <span class="text-gray-600 font-mono break-words">{error.message}</span>
          </div>
        {/if}
        {#if error.timestamp}
          <div class="text-xs">
            <span class="font-medium text-gray-700">Time:</span>
            <span class="text-gray-600 font-mono">{new Date(error.timestamp).toLocaleString()}</span>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<!-- Inline Error Variant -->
{#if variant === "inline"}
  <div class="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800" 
       role="alert">
    <svg class="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" 
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
            clip-rule="evenodd" />
    </svg>
    <span class="text-sm font-medium">{errorMessage}</span>
    {#if showRetry}
      <button
        class="ml-auto text-red-600 hover:text-red-800 text-sm font-medium
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"
        on:click={handleRetry}
        aria-label="Retry the failed operation"
      >
        Retry
      </button>
    {/if}
  </div>
{/if}

<!-- Toast Error Variant -->
{#if variant === "toast"}
  <div class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-red-200 rounded-lg shadow-lg" 
       role="alert">
    <div class="p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                  clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 w-0 flex-1">
          <p class="text-sm font-medium text-gray-900">Error</p>
          <p class="mt-1 text-sm text-gray-500">{errorMessage}</p>
          {#if showRetry}
            <div class="mt-3">
              <button
                class="text-sm font-medium text-red-600 hover:text-red-500
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"
                on:click={handleRetry}
              >
                {retryText}
              </button>
            </div>
          {/if}
        </div>
        <div class="ml-4 flex-shrink-0 flex">
          <button
            class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500
                   focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            on:click={() => dispatch("dismiss")}
            aria-label="Dismiss error"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" 
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
                    clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
