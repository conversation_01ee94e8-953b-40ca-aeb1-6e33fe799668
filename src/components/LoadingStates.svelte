<!--
  LoadingStates.svelte
  
  Reusable loading state components with skeleton loaders and shimmer effects.
  
  Features:
  - Multiple loading state variants (cards, details, lists)
  - Responsive design with Tailwind CSS
  - Accessible loading indicators with ARIA labels
  - Shimmer animation effects
  - Customizable loading messages
  
  Usage:
  <LoadingStates variant="cards" count={6} />
  <LoadingStates variant="detail" />
  <LoadingStates variant="list" count={10} />
-->

<script>
  export let variant = "cards"; // "cards", "detail", "list", "inline"
  export let count = 3; // Number of skeleton items to show
  export let message = "Loading..."; // Loading message
  export let showMessage = true; // Whether to show loading message
</script>

{#if variant === "cards"}
  <!-- Card Grid Skeleton -->
  <div class="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
       role="status" 
       aria-label="Loading content">
    {#each Array(count) as _, i}
      <div class="bg-white rounded-lg shadow-md border border-gray-100 min-h-[280px] p-6 animate-pulse">
        <!-- Header skeleton -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
            <div class="w-16 h-5 bg-gray-200 rounded"></div>
          </div>
          <div class="w-16 h-6 bg-gray-200 rounded-full"></div>
        </div>
        
        <!-- Policy number skeleton -->
        <div class="w-32 h-4 bg-gray-200 rounded mb-4"></div>
        
        <!-- Details skeleton -->
        <div class="space-y-3 mb-4">
          <div class="flex justify-between">
            <div class="w-24 h-4 bg-gray-200 rounded"></div>
            <div class="w-20 h-4 bg-gray-200 rounded"></div>
          </div>
          <div class="flex justify-between">
            <div class="w-28 h-4 bg-gray-200 rounded"></div>
            <div class="w-16 h-4 bg-gray-200 rounded"></div>
          </div>
          <div class="flex justify-between">
            <div class="w-16 h-4 bg-gray-200 rounded"></div>
            <div class="w-20 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
        
        <!-- Description skeleton -->
        <div class="border-t border-gray-100 pt-4">
          <div class="space-y-2">
            <div class="w-full h-3 bg-gray-200 rounded"></div>
            <div class="w-3/4 h-3 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    {/each}
    
    {#if showMessage}
      <div class="col-span-full text-center py-4">
        <div class="inline-flex items-center gap-2 text-gray-600">
          <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-sm font-medium">{message}</span>
        </div>
      </div>
    {/if}
  </div>

{:else if variant === "detail"}
  <!-- Detail Page Skeleton -->
  <div role="status" aria-label="Loading policy details">
    <!-- Header skeleton -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 animate-pulse">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-center mb-4 lg:mb-0">
          <div class="w-12 h-12 bg-gray-200 rounded mr-4"></div>
          <div>
            <div class="w-48 h-8 bg-gray-200 rounded mb-2"></div>
            <div class="w-32 h-5 bg-gray-200 rounded"></div>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row sm:items-center gap-3">
          <div class="w-20 h-8 bg-gray-200 rounded-full"></div>
          <div class="text-right">
            <div class="w-24 h-8 bg-gray-200 rounded mb-1"></div>
            <div class="w-16 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Content skeleton -->
    <div class="grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4">
      <!-- Main content area -->
      <div class="lg:col-span-2 xl:col-span-3 space-y-6">
        {#each Array(3) as _, i}
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div class="w-32 h-6 bg-gray-200 rounded mb-4"></div>
            <div class="space-y-4">
              {#each Array(4) as _, j}
                <div class="flex justify-between">
                  <div class="w-24 h-4 bg-gray-200 rounded"></div>
                  <div class="w-20 h-4 bg-gray-200 rounded"></div>
                </div>
              {/each}
            </div>
          </div>
        {/each}
      </div>
      
      <!-- Sidebar skeleton -->
      <div class="space-y-6">
        {#each Array(2) as _, i}
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div class="w-24 h-6 bg-gray-200 rounded mb-4"></div>
            <div class="space-y-3">
              {#each Array(3) as _, j}
                <div class="w-full h-4 bg-gray-200 rounded"></div>
              {/each}
            </div>
          </div>
        {/each}
      </div>
    </div>
    
    {#if showMessage}
      <div class="text-center py-8">
        <div class="inline-flex items-center gap-2 text-gray-600">
          <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-sm font-medium">{message}</span>
        </div>
      </div>
    {/if}
  </div>

{:else if variant === "list"}
  <!-- List Skeleton -->
  <div class="space-y-4" role="status" aria-label="Loading list">
    {#each Array(count) as _, i}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
            <div>
              <div class="w-32 h-5 bg-gray-200 rounded mb-1"></div>
              <div class="w-24 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
          <div class="w-16 h-6 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    {/each}
    
    {#if showMessage}
      <div class="text-center py-4">
        <div class="inline-flex items-center gap-2 text-gray-600">
          <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-sm font-medium">{message}</span>
        </div>
      </div>
    {/if}
  </div>

{:else if variant === "inline"}
  <!-- Inline Loading -->
  <div class="inline-flex items-center gap-2 text-gray-600" role="status" aria-label="Loading">
    <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {#if showMessage}
      <span class="text-sm">{message}</span>
    {/if}
  </div>
{/if}

<style>
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
  
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
