<!--
  PolicyList.svelte
  
  A responsive insurance policy list component that displays customer policies in a card layout.
  
  Features:
  - Responsive CSS Grid layout (1-4 cards per row based on screen size)
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges
  - Professional card design with policy details
  
  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row  
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
  
  Usage:
  <PolicyList />
  
  Future Enhancement: Accept policies as props
  <PolicyList {policies} />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";
  import { policiesStore, loadPolicies } from "./stores/dataStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";

  const dispatch = createEventDispatcher();

  // Reactive store subscriptions
  $: policies = $policiesStore.data || [];
  $: loading = $policiesStore.loading;
  $: error = $policiesStore.error;

  // Demo parameters - in a real app, these would come from user input or URL params
  const searchParams = {
    citizenId: "1234567890123", // Default citizen ID for demo
  };

  // Policy type icons mapping
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Business: "🏢",
  };

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  // Format currency with decimals for premiums
  function formatPremium(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  // Format date
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  }

  // Handle policy card click to navigate to detail page
  function handlePolicyClick(policyId) {
    dispatch("navigate", {
      page: "policy-detail",
      policyId: policyId,
    });
  }

  // Load policies data
  async function loadPoliciesData() {
    try {
      await loadPolicies(searchParams);
      console.log(
        "Policies loaded successfully:",
        $policiesStore.data?.length || 0,
      );
    } catch (error) {
      console.error("Failed to load policies:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPoliciesData();
  }

  onMount(() => {
    console.log("PolicyList component mounted");
    loadPoliciesData();
  });
</script>

<main class="p-4" aria-label="Insurance Policy List">
  <header class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Your Insurance Policies
    </h1>
    <p class="text-gray-600">
      Manage and view all your active insurance policies
    </p>
  </header>

  <!-- Loading State -->
  {#if loading}
    <LoadingStates
      variant="cards"
      count={6}
      message="Loading your policies..."
    />

    <!-- Error State -->
  {:else if error}
    <ErrorStates
      variant="api"
      {error}
      on:retry={handleRetry}
      message="Failed to load your policies. Please try again."
    />

    <!-- Empty State -->
  {:else if policies.length === 0}
    <div class="text-center py-12">
      <div class="text-6xl mb-4" aria-hidden="true">📄</div>
      <h2 class="text-2xl font-bold text-gray-900 mb-2">No Policies Found</h2>
      <p class="text-gray-600 mb-6">
        We couldn't find any insurance policies for your account.
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleRetry}
        aria-label="Refresh policies"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Refresh
      </button>
    </div>

    <!-- Policies Grid -->
  {:else}
    <section
      class="grid gap-6
             grid-cols-1
             sm:grid-cols-2
             lg:grid-cols-3
             xl:grid-cols-4"
      aria-label="Policy cards grid"
    >
      {#each policies as policy (policy.id)}
        <button
          class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[280px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
          aria-labelledby="policy-{policy.id}-title"
          on:click={() => handlePolicyClick(policy.id)}
        >
          <!-- Policy Header -->
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h2
                id="policy-{policy.id}-title"
                class="text-lg font-semibold text-gray-900 flex items-center gap-2"
              >
                <span class="text-2xl" aria-hidden="true"
                  >{typeIcons[policy.type]}</span
                >
                {policy.type}
              </h2>
              <span
                class="px-3 py-1 rounded-full text-xs font-medium border
                     {statusColors[policy.status]}"
                aria-label="Policy status: {policy.status}"
              >
                {policy.status}
              </span>
            </div>

            <p class="text-sm text-gray-600 mb-2">
              Policy #{policy.policyNumber}
            </p>
          </div>

          <!-- Policy Details -->
          <div class="space-y-3 mb-4 flex-grow">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Coverage Amount:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatCurrency(policy.coverageAmount)}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Monthly Premium:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatPremium(policy.monthlyPremium)}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Expires:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatDate(policy.expirationDate)}
              </span>
            </div>
          </div>

          <!-- Policy Description -->
          <div class="border-t border-gray-100 pt-4">
            <p class="text-xs text-gray-600 leading-relaxed line-clamp-3">
              {policy.description}
            </p>
          </div>
        </button>
      {/each}
    </section>
  {/if}
</main>
