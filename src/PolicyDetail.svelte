<!--
  PolicyDetail.svelte

  Comprehensive policy detail component that displays detailed policy information.

  Features:
  - Comprehensive policy information display
  - Policy status and key dates section
  - Action buttons for policy management
  - Related documents and claims sections
  - Responsive design with Tailwind CSS utility classes
  - Semantic HTML structure with ARIA labels
  - Back navigation functionality

  Usage:
  <PolicyDetail {selectedPolicyId} on:navigate />
-->

<script>
  import { createEventDispatcher, onMount } from "svelte";
  import { policyDetailStore, loadPolicyDetail } from "./stores/dataStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";

  export let selectedPolicyId = null;

  const dispatch = createEventDispatcher();

  // Reactive store subscriptions
  $: policyDetail = $policyDetailStore.data;
  $: loading = $policyDetailStore.loading;
  $: error = $policyDetailStore.error;

  // Extended mock data structure with additional fields for detailed view
  const policies = [
    {
      id: "pol-001",
      policyNumber: "AUTO-2024-001234",
      type: "Auto",
      coverageAmount: 250000,
      monthlyPremium: 185.5,
      status: "Active",
      expirationDate: "2025-06-15T00:00:00Z",
      effectiveDate: "2024-06-15T00:00:00Z",
      renewalDate: "2025-06-15T00:00:00Z",
      description:
        "Comprehensive auto coverage including collision, liability, and uninsured motorist protection.",
      insurer: "SafeDrive Insurance Co.",
      agent: {
        name: "Sarah Johnson",
        phone: "(*************",
        email: "<EMAIL>",
      },
      coverageDetails: {
        liability: { limit: 100000, deductible: 0 },
        collision: { limit: 50000, deductible: 500 },
        comprehensive: { limit: 50000, deductible: 250 },
        uninsuredMotorist: { limit: 100000, deductible: 0 },
      },
      vehicle: {
        year: 2022,
        make: "Toyota",
        model: "Camry",
        vin: "1HGBH41JXMN109186",
      },
    },
    {
      id: "pol-002",
      policyNumber: "HOME-2024-005678",
      type: "Home",
      coverageAmount: 450000,
      monthlyPremium: 125.75,
      status: "Active",
      expirationDate: "2025-03-22T00:00:00Z",
      effectiveDate: "2024-03-22T00:00:00Z",
      renewalDate: "2025-03-22T00:00:00Z",
      description:
        "Full homeowners insurance with dwelling, personal property, and liability coverage.",
      insurer: "HomeGuard Insurance",
      agent: {
        name: "Michael Chen",
        phone: "(*************",
        email: "<EMAIL>",
      },
      coverageDetails: {
        dwelling: { limit: 300000, deductible: 1000 },
        personalProperty: { limit: 150000, deductible: 500 },
        liability: { limit: 300000, deductible: 0 },
        medicalPayments: { limit: 5000, deductible: 0 },
      },
      property: {
        address: "123 Main Street, Anytown, ST 12345",
        yearBuilt: 1995,
        squareFootage: 2400,
        propertyType: "Single Family Home",
      },
    },
    {
      id: "pol-003",
      policyNumber: "LIFE-2024-009876",
      type: "Life",
      coverageAmount: 500000,
      monthlyPremium: 89.25,
      status: "Pending",
      expirationDate: "2025-12-31T00:00:00Z",
      effectiveDate: "2024-12-31T00:00:00Z",
      renewalDate: "2025-12-31T00:00:00Z",
      description:
        "Term life insurance policy providing financial security for beneficiaries.",
      insurer: "LifeSecure Insurance",
      agent: {
        name: "Emily Rodriguez",
        phone: "(*************",
        email: "<EMAIL>",
      },
      coverageDetails: {
        deathBenefit: { limit: 500000, deductible: 0 },
        accidentalDeath: { limit: 500000, deductible: 0 },
      },
      beneficiaries: [
        { name: "Jane Doe", relationship: "Spouse", percentage: 60 },
        { name: "John Doe Jr.", relationship: "Child", percentage: 40 },
      ],
    },
  ];

  // Navigation handler
  function handleBackNavigation() {
    dispatch("navigate", { page: "policy-list" });
  }

  // Mock documents data
  const documents = [
    {
      id: "doc-001",
      name: "Policy Certificate",
      type: "PDF",
      size: "245 KB",
      date: "2024-06-15",
    },
    {
      id: "doc-002",
      name: "Coverage Summary",
      type: "PDF",
      size: "156 KB",
      date: "2024-06-15",
    },
    {
      id: "doc-003",
      name: "Terms & Conditions",
      type: "PDF",
      size: "892 KB",
      date: "2024-06-15",
    },
  ];

  // Mock claims data
  const claims = [
    {
      id: "claim-001",
      number: "CLM-2024-001",
      status: "Approved",
      amount: 2500,
      date: "2024-08-15",
      description: "Minor collision repair",
    },
    {
      id: "claim-002",
      number: "CLM-2024-002",
      status: "Processing",
      amount: 850,
      date: "2024-09-22",
      description: "Windshield replacement",
    },
  ];

  // Policy type icons mapping
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Business: "🏢",
  };

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Utility functions
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  function formatPremium(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  }

  function formatShortDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  }

  // Load policy detail data
  async function loadPolicyDetailData() {
    if (!selectedPolicyId) return;

    try {
      // Convert policy ID to member code for API call
      const memberCode = `MEM${selectedPolicyId.replace("pol-", "")}`;
      await loadPolicyDetail(memberCode);
      console.log("Policy detail loaded successfully");
    } catch (error) {
      console.error("Failed to load policy detail:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPolicyDetailData();
  }

  // Watch for selectedPolicyId changes and load data
  $: if (selectedPolicyId) {
    loadPolicyDetailData();
  }

  // Find the selected policy (fallback to mock data if API data not available)
  $: selectedPolicy =
    policyDetail ||
    (selectedPolicyId ? policies.find((p) => p.id === selectedPolicyId) : null);

  // Action handlers
  function handleEditPolicy() {
    alert("Edit policy functionality would be implemented here");
  }

  function handleRenewPolicy() {
    alert("Renew policy functionality would be implemented here");
  }

  function handleCancelPolicy() {
    if (confirm("Are you sure you want to cancel this policy?")) {
      alert("Cancel policy functionality would be implemented here");
    }
  }

  function handleDownloadDocument(docId) {
    alert(`Download document ${docId} functionality would be implemented here`);
  }

  function handleViewClaim(claimId) {
    alert(`View claim ${claimId} functionality would be implemented here`);
  }
</script>

{#if !selectedPolicyId}
  <!-- No Policy Selected State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to policy list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Policy List
      </button>
    </div>

    <div class="max-w-4xl mx-auto text-center">
      <div class="text-8xl mb-6" aria-hidden="true">📄</div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">No Policy Selected</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
        Please select a policy from the policy list to view detailed
        information.
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go to policy list"
      >
        View Policy List
      </button>
    </div>
  </main>
{:else if loading}
  <!-- Loading State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to policy list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Policy List
      </button>
    </div>
    <LoadingStates variant="detail" message="Loading policy details..." />
  </main>
{:else if error}
  <!-- Error State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to policy list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Policy List
      </button>
    </div>
    <ErrorStates
      variant="api"
      {error}
      on:retry={handleRetry}
      on:goBack={handleBackNavigation}
      message="Failed to load policy details. Please try again."
    />
  </main>
{:else if !selectedPolicy}
  <!-- Policy Not Found State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to policy list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Policy List
      </button>
    </div>
    <ErrorStates
      variant="not-found"
      on:goBack={handleBackNavigation}
      message="The requested policy could not be found."
    />
  </main>
{:else}
  <!-- Policy Detail Content -->
  <main class="min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8">
    <!-- Back Navigation -->
    <div class="max-w-7xl mx-auto mb-6 sm:mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to policy list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Policy List
      </button>
    </div>

    <!-- Main Content Container -->
    <div class="max-w-7xl mx-auto">
      <!-- Policy Header -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <div
          class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
        >
          <div class="flex items-center mb-4 lg:mb-0">
            <span class="text-4xl mr-4" aria-hidden="true"
              >{typeIcons[selectedPolicy.type]}</span
            >
            <div>
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                {selectedPolicy.type} Insurance Policy
              </h1>
              <p class="text-gray-600">Policy #{selectedPolicy.policyNumber}</p>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row sm:items-center gap-3">
            <span
              class="px-4 py-2 rounded-full text-sm font-medium border text-center
                     {statusColors[selectedPolicy.status]}"
              aria-label="Policy status: {selectedPolicy.status}"
            >
              {selectedPolicy.status}
            </span>
            <div class="text-right">
              <div class="text-2xl font-bold text-gray-900">
                {formatPremium(selectedPolicy.monthlyPremium)}
              </div>
              <div class="text-sm text-gray-500">per month</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Responsive Grid Layout -->
      <div
        class="grid gap-6
                  grid-cols-1
                  lg:grid-cols-3
                  xl:grid-cols-4"
      >
        <!-- Main Content Area -->
        <div class="lg:col-span-2 xl:col-span-3 space-y-6">
          <!-- Policy Overview -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Policy Overview
            </h2>
            <div class="grid gap-4 sm:grid-cols-2">
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Coverage Amount</label
                >
                <p class="text-lg font-semibold text-gray-900">
                  {formatCurrency(selectedPolicy.coverageAmount)}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Monthly Premium</label
                >
                <p class="text-lg font-semibold text-gray-900">
                  {formatPremium(selectedPolicy.monthlyPremium)}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Effective Date</label
                >
                <p class="text-gray-900">
                  {formatDate(selectedPolicy.effectiveDate)}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Expiration Date</label
                >
                <p class="text-gray-900">
                  {formatDate(selectedPolicy.expirationDate)}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Renewal Date</label
                >
                <p class="text-gray-900">
                  {formatDate(selectedPolicy.renewalDate)}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Insurance Company</label
                >
                <p class="text-gray-900">{selectedPolicy.insurer}</p>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
              <label class="block text-sm font-medium text-gray-500 mb-2"
                >Description</label
              >
              <p class="text-gray-700 leading-relaxed">
                {selectedPolicy.description}
              </p>
            </div>
          </section>

          <!-- Coverage Details -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Coverage Details
            </h2>
            <div class="space-y-4">
              {#each Object.entries(selectedPolicy.coverageDetails) as [coverageType, details]}
                <div
                  class="flex justify-between items-center p-4 bg-gray-50 rounded-lg"
                >
                  <div>
                    <h3 class="font-medium text-gray-900 capitalize">
                      {coverageType.replace(/([A-Z])/g, " $1").trim()}
                    </h3>
                    <p class="text-sm text-gray-600">
                      Limit: {formatCurrency(details.limit)}
                      {#if details.deductible > 0}
                        • Deductible: {formatCurrency(details.deductible)}
                      {/if}
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-semibold text-gray-900">
                      {formatCurrency(details.limit)}
                    </div>
                    {#if details.deductible > 0}
                      <div class="text-sm text-gray-500">
                        {formatCurrency(details.deductible)} deductible
                      </div>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          </section>

          <!-- Additional Policy Information (Auto-specific example) -->
          {#if selectedPolicy.vehicle}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Vehicle Information
              </h2>
              <div class="grid gap-4 sm:grid-cols-2">
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Year</label
                  >
                  <p class="text-gray-900">{selectedPolicy.vehicle.year}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Make & Model</label
                  >
                  <p class="text-gray-900">
                    {selectedPolicy.vehicle.make}
                    {selectedPolicy.vehicle.model}
                  </p>
                </div>
                <div class="sm:col-span-2">
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >VIN</label
                  >
                  <p class="text-gray-900 font-mono">
                    {selectedPolicy.vehicle.vin}
                  </p>
                </div>
              </div>
            </section>
          {/if}

          {#if selectedPolicy.property}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Property Information
              </h2>
              <div class="grid gap-4 sm:grid-cols-2">
                <div class="sm:col-span-2">
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Address</label
                  >
                  <p class="text-gray-900">{selectedPolicy.property.address}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Year Built</label
                  >
                  <p class="text-gray-900">
                    {selectedPolicy.property.yearBuilt}
                  </p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Square Footage</label
                  >
                  <p class="text-gray-900">
                    {selectedPolicy.property.squareFootage.toLocaleString()} sq ft
                  </p>
                </div>
                <div class="sm:col-span-2">
                  <label class="block text-sm font-medium text-gray-500 mb-1"
                    >Property Type</label
                  >
                  <p class="text-gray-900">
                    {selectedPolicy.property.propertyType}
                  </p>
                </div>
              </div>
            </section>
          {/if}

          {#if selectedPolicy.beneficiaries}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Beneficiaries
              </h2>
              <div class="space-y-3">
                {#each selectedPolicy.beneficiaries as beneficiary}
                  <div
                    class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <p class="font-medium text-gray-900">
                        {beneficiary.name}
                      </p>
                      <p class="text-sm text-gray-600">
                        {beneficiary.relationship}
                      </p>
                    </div>
                    <div class="text-right">
                      <p class="font-semibold text-gray-900">
                        {beneficiary.percentage}%
                      </p>
                    </div>
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Claims History -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Recent Claims
            </h2>
            {#if claims.length > 0}
              <div class="space-y-3">
                {#each claims as claim}
                  <div
                    class="flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div>
                      <p class="font-medium text-gray-900">
                        Claim #{claim.number}
                      </p>
                      <p class="text-sm text-gray-600">{claim.description}</p>
                      <p class="text-xs text-gray-500">
                        {formatShortDate(claim.date)}
                      </p>
                    </div>
                    <div class="text-right">
                      <p class="font-semibold text-gray-900">
                        {formatCurrency(claim.amount)}
                      </p>
                      <span
                        class="inline-block px-2 py-1 text-xs rounded-full
                                   {claim.status === 'Approved'
                          ? 'bg-green-100 text-green-800'
                          : claim.status === 'Processing'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'}"
                      >
                        {claim.status}
                      </span>
                      <button
                        class="block mt-1 text-xs text-blue-600 hover:text-blue-800"
                        on:click={() => handleViewClaim(claim.id)}
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                {/each}
              </div>
            {:else}
              <div class="text-center py-8">
                <div class="text-4xl mb-2" aria-hidden="true">📋</div>
                <p class="text-gray-500">No claims found for this policy</p>
              </div>
            {/if}
          </section>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 xl:col-span-1 space-y-6">
          <!-- Action Buttons -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Policy Actions
            </h2>
            <div class="space-y-3">
              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-blue-600 hover:bg-blue-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                on:click={handleEditPolicy}
                aria-label="Edit policy details"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                Edit Policy
              </button>

              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-green-600 hover:bg-green-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                on:click={handleRenewPolicy}
                aria-label="Renew policy"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Renew Policy
              </button>

              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-red-600 hover:bg-red-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                on:click={handleCancelPolicy}
                aria-label="Cancel policy"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                Cancel Policy
              </button>
            </div>
          </section>

          <!-- Agent Information -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Your Agent</h2>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Name</label
                >
                <p class="text-gray-900">{selectedPolicy.agent.name}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Phone</label
                >
                <a
                  href="tel:{selectedPolicy.agent.phone}"
                  class="text-blue-600 hover:text-blue-800"
                >
                  {selectedPolicy.agent.phone}
                </a>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500 mb-1"
                  >Email</label
                >
                <a
                  href="mailto:{selectedPolicy.agent.email}"
                  class="text-blue-600 hover:text-blue-800 break-all"
                >
                  {selectedPolicy.agent.email}
                </a>
              </div>
            </div>
          </section>

          <!-- Documents -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Policy Documents
            </h2>
            <div class="space-y-3">
              {#each documents as document}
                <div
                  class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center">
                    <svg
                      class="w-5 h-5 text-red-500 mr-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        {document.name}
                      </p>
                      <p class="text-xs text-gray-500">
                        {document.size} • {formatShortDate(document.date)}
                      </p>
                    </div>
                  </div>
                  <button
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    on:click={() => handleDownloadDocument(document.id)}
                    aria-label="Download {document.name}"
                  >
                    Download
                  </button>
                </div>
              {/each}
            </div>
          </section>
        </div>
      </div>
    </div>
  </main>
{/if}
