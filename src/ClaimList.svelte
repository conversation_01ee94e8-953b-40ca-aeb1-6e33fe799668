<!--
  ClaimList.svelte
  
  A responsive insurance claim list component that displays customer claims in a card layout.
  
  Features:
  - Responsive CSS Grid layout (1-4 cards per row based on screen size)
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges
  - Professional card design with claim details
  
  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row  
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
  
  Usage:
  <ClaimList on:navigate />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";
  import { claimsStore, loadClaims } from "./stores/dataStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";

  const dispatch = createEventDispatcher();

  // Reactive store subscriptions
  $: claims = $claimsStore.data || [];
  $: loading = $claimsStore.loading;
  $: error = $claimsStore.error;

  // Demo parameters - in a real app, these would come from user input or URL params
  const searchParams = {
    memberCode: "MEM001", // Default member code for demo
  };

  // Claim type icons mapping
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Health: "🏥",
    Life: "❤️",
    Business: "🏢",
  };

  // Status color mapping
  const statusColors = {
    Approved: "bg-green-100 text-green-800 border-green-200",
    Processing: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Denied: "bg-red-100 text-red-800 border-red-200",
    "Under Review": "bg-blue-100 text-blue-800 border-blue-200",
    Pending: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  }

  // Format date
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  }

  // Handle claim card click to navigate to detail page
  function handleClaimClick(claimId) {
    dispatch("navigate", {
      page: "claim-detail",
      claimId: claimId,
    });
  }

  // Load claims data
  async function loadClaimsData() {
    try {
      await loadClaims(searchParams);
      console.log(
        "Claims loaded successfully:",
        $claimsStore.data?.length || 0,
      );
    } catch (error) {
      console.error("Failed to load claims:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadClaimsData();
  }

  onMount(() => {
    console.log("ClaimList component mounted");
    loadClaimsData();
  });
</script>

<main class="p-4" aria-label="Insurance Claim List">
  <header class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Your Insurance Claims</h1>
    <p class="text-gray-600">Track and manage all your insurance claims</p>
  </header>

  <!-- Loading State -->
  {#if loading}
    <LoadingStates variant="cards" count={6} message="Loading your claims..." />

    <!-- Error State -->
  {:else if error}
    <ErrorStates
      variant="api"
      {error}
      on:retry={handleRetry}
      message="Failed to load your claims. Please try again."
    />

    <!-- Empty State -->
  {:else if claims.length === 0}
    <div class="text-center py-12">
      <div class="text-6xl mb-4" aria-hidden="true">📋</div>
      <h2 class="text-2xl font-bold text-gray-900 mb-2">No Claims Found</h2>
      <p class="text-gray-600 mb-6">
        We couldn't find any insurance claims for your account.
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleRetry}
        aria-label="Refresh claims"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Refresh
      </button>
    </div>

    <!-- Claims Grid -->
  {:else}
    <section
      class="grid gap-6
             grid-cols-1
             sm:grid-cols-2
             lg:grid-cols-3
             xl:grid-cols-4"
      aria-label="Claim cards grid"
    >
      {#each claims as claim (claim.id)}
        <button
          class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[280px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
          aria-labelledby="claim-{claim.id}-title"
          on:click={() => handleClaimClick(claim.id)}
        >
          <!-- Claim Header -->
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h2
                id="claim-{claim.id}-title"
                class="text-lg font-semibold text-gray-900 flex items-center gap-2"
              >
                <span class="text-2xl" aria-hidden="true"
                  >{typeIcons[claim.type]}</span
                >
                {claim.type}
              </h2>
              <span
                class="px-3 py-1 rounded-full text-xs font-medium border
                     {statusColors[claim.status]}"
                aria-label="Claim status: {claim.status}"
              >
                {claim.status}
              </span>
            </div>
            <p class="text-sm text-gray-600 font-mono">
              {claim.claimNumber}
            </p>
          </div>

          <!-- Claim Details -->
          <div class="space-y-3 mb-4 flex-grow">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Claim Amount:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatCurrency(claim.amount)}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Deductible:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatCurrency(claim.deductible)}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Date of Loss:</span>
              <span class="text-sm font-medium text-gray-900">
                {formatDate(claim.dateOfLoss)}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Policy:</span>
              <span class="text-sm font-medium text-gray-900 font-mono">
                {claim.policyNumber}
              </span>
            </div>
          </div>

          <!-- Claim Description -->
          <div class="border-t border-gray-100 pt-3">
            <p class="text-sm text-gray-600 line-clamp-2">
              {claim.description}
            </p>
          </div>
        </button>
      {/each}
    </section>
  {/if}
</main>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }
</style>
