/**
 * TPA API Client Tests
 * 
 * Comprehensive tests for the HTTP client including retry logic,
 * error handling, and request/response processing.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TpaApiClient, defaultClient } from '../../src/api/client.js';
import { ApiError, NetworkError, TimeoutError } from '../../src/api/errors.js';
import { mockApiResponse, mockApiError, mockNetworkError, mockTimeoutError } from '../setup.js';

describe('TpaApiClient', () => {
  let client;

  beforeEach(() => {
    client = new TpaApiClient({
      baseUrl: 'http://localhost:9000/api',
      timeout: 5000,
      retryAttempts: 2,
      retryDelay: 100
    });
  });

  describe('constructor', () => {
    it('should create client with default configuration', () => {
      const defaultClient = new TpaApiClient();
      expect(defaultClient.baseUrl).toBe('http://localhost:9000/api');
      expect(defaultClient.timeout).toBe(30000);
      expect(defaultClient.retryAttempts).toBe(3);
    });

    it('should create client with custom configuration', () => {
      const customClient = new TpaApiClient({
        baseUrl: 'http://custom.api.com',
        timeout: 10000,
        retryAttempts: 5
      });

      expect(customClient.baseUrl).toBe('http://custom.api.com');
      expect(customClient.timeout).toBe(10000);
      expect(customClient.retryAttempts).toBe(5);
    });
  });

  describe('request interceptors', () => {
    it('should add and apply request interceptors', async () => {
      const interceptor = vi.fn((config) => ({
        ...config,
        headers: { ...config.headers, 'X-Custom': 'test' }
      }));

      client.addRequestInterceptor(interceptor);

      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }));

      await client.request('/test');

      expect(interceptor).toHaveBeenCalled();
      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Custom': 'test'
          })
        })
      );
    });

    it('should apply multiple request interceptors in order', async () => {
      const interceptor1 = vi.fn((config) => ({
        ...config,
        headers: { ...config.headers, 'X-First': 'first' }
      }));

      const interceptor2 = vi.fn((config) => ({
        ...config,
        headers: { ...config.headers, 'X-Second': 'second' }
      }));

      client.addRequestInterceptor(interceptor1);
      client.addRequestInterceptor(interceptor2);

      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }));

      await client.request('/test');

      expect(interceptor1).toHaveBeenCalled();
      expect(interceptor2).toHaveBeenCalled();
      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-First': 'first',
            'X-Second': 'second'
          })
        })
      );
    });
  });

  describe('response interceptors', () => {
    it('should add and apply response interceptors', async () => {
      const interceptor = vi.fn((response) => response);

      client.addResponseInterceptor(interceptor);

      const mockResponse = mockApiResponse({ success: true });
      fetch.mockResolvedValueOnce(mockResponse);

      await client.request('/test');

      expect(interceptor).toHaveBeenCalled();
    });
  });

  describe('retry logic', () => {
    it('should retry on network errors', async () => {
      fetch
        .mockRejectedValueOnce(new TypeError('Failed to fetch'))
        .mockRejectedValueOnce(new TypeError('Failed to fetch'))
        .mockResolvedValueOnce(mockApiResponse({ success: true }));

      const result = await client.request('/test');

      expect(fetch).toHaveBeenCalledTimes(3);
      expect(result).toEqual({ success: true });
    });

    it('should retry on server errors (5xx)', async () => {
      fetch
        .mockResolvedValueOnce(mockApiError(500, 'Internal Server Error'))
        .mockResolvedValueOnce(mockApiError(502, 'Bad Gateway'))
        .mockResolvedValueOnce(mockApiResponse({ success: true }));

      const result = await client.request('/test');

      expect(fetch).toHaveBeenCalledTimes(3);
      expect(result).toEqual({ success: true });
    });

    it('should not retry on client errors (4xx)', async () => {
      fetch.mockResolvedValueOnce(mockApiError(400, 'Bad Request'));

      await expect(client.request('/test')).rejects.toThrow(ApiError);
      expect(fetch).toHaveBeenCalledTimes(1);
    });

    it('should respect retry attempts limit', async () => {
      fetch.mockRejectedValue(new TypeError('Failed to fetch'));

      await expect(client.request('/test')).rejects.toThrow(NetworkError);
      expect(fetch).toHaveBeenCalledTimes(3); // 1 initial + 2 retries
    });

    it('should calculate exponential backoff delay', () => {
      expect(client.calculateRetryDelay(0)).toBeGreaterThanOrEqual(100);
      expect(client.calculateRetryDelay(1)).toBeGreaterThanOrEqual(200);
      expect(client.calculateRetryDelay(2)).toBeGreaterThanOrEqual(400);
    });
  });

  describe('error handling', () => {
    it('should handle network errors', async () => {
      fetch.mockRejectedValueOnce(new TypeError('Failed to fetch'));

      await expect(client.request('/test')).rejects.toThrow(NetworkError);
    });

    it('should handle API errors with status codes', async () => {
      fetch.mockResolvedValueOnce(mockApiError(404, 'Not Found'));

      await expect(client.request('/test')).rejects.toThrow(ApiError);
    });

    it('should parse error response JSON', async () => {
      const errorData = {
        error: 'Validation Error',
        message: 'Invalid parameters',
        details: { field: 'INSURER_CODE' }
      };

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve(errorData)
      });

      try {
        await client.request('/test');
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.status).toBe(400);
        expect(error.data).toEqual(errorData);
      }
    });
  });

  describe('GET requests', () => {
    it('should make GET request with query parameters', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ data: [] }));

      await client.get('/test', { param1: 'value1', param2: 'value2' });

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:9000/api/test?param1=value1&param2=value2',
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle empty parameters', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ data: [] }));

      await client.get('/test', {});

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:9000/api/test',
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should filter out null/undefined/empty parameters', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ data: [] }));

      await client.get('/test', {
        valid: 'value',
        null_param: null,
        undefined_param: undefined,
        empty_param: '',
        zero_param: 0
      });

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:9000/api/test?valid=value&zero_param=0',
        expect.any(Object)
      );
    });
  });

  describe('health check', () => {
    it('should make health check request', async () => {
      const healthData = { status: 'healthy', timestamp: '2024-01-01T00:00:00Z' };
      fetch.mockResolvedValueOnce(mockApiResponse(healthData));

      const result = await client.healthCheck();

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:9000/api/health',
        expect.objectContaining({
          method: 'GET'
        })
      );
      expect(result).toEqual(healthData);
    });
  });

  describe('timeout handling', () => {
    it('should create timeout controller', () => {
      const { controller, timeoutId } = client.createTimeoutController();

      expect(controller).toBeInstanceOf(AbortController);
      expect(timeoutId).toBeDefined();

      clearTimeout(timeoutId);
    });
  });
});

describe('defaultClient', () => {
  it('should export default client instance', () => {
    expect(defaultClient).toBeInstanceOf(TpaApiClient);
  });

  it('should use environment configuration', () => {
    expect(defaultClient.baseUrl).toBe('http://localhost:9000/api');
  });
});
